import { Octokit, App } from "octokit";
const octokit = new Octokit({ auth: `****************************************` });
let repoUrl="https://github.com/vercel/next.js";
async function getRepoDetail(repoUrl: string) {
    const match = repoUrl.match(/github\.com\/([^\/]+)\/([^\/]+)/);
    if (!match) throw new Error('Invalid GitHub repository URL');
    const [owner, repo] = match.slice(1);
    const [{data:repoInfo},{data:launguage},{data:commits},{data:contributors},{data:content}] = await Promise.all([
        octokit.request(`GET /repos/${owner}/${repo}`),
        octokit.request(`GET /repos/${owner}/${repo}/languages`),
        octokit.request(`GET /repos/${owner}/${repo}/commits`),
        octokit.request(`GET /repos/${owner}/${repo}/contributors`),
        octokit.request(`GET /repos/${owner}/${repo}/contents`)
    ]);
    return{
        name:repoInfo.name,
        description:repoInfo.description,
        language:launguage,
        commits:commits,
        contributors:contributors,
        content:content
    }
}
getRepoDetail(repoUrl).then((data) => {
    console.log(data);
}).catch((err) => {
    console.log(err);
});
