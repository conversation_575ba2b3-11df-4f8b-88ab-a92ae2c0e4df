import { Octokit } from "octokit";
import dotenv from "dotenv";
dotenv.config();
const octokit = new Octokit({
    auth: process.env.GITHUB_TOKEN
});
let repoUrl="https://github.com/vercel/next.js";
async function getRepoDetail(repoUrl: string) {
    const match = repoUrl.match(/github\.com\/([^\/]+)\/([^\/]+)/);
    if (!match) throw new Error('Invalid GitHub repository URL');
    const [owner, repo] = match.slice(1);
    const [{data:repoInfo},{data:language},{data:commits},{data:contributors},{data:content}] = await Promise.all([
        octokit.request(`GET /repos/${owner}/${repo}`),
        octokit.request(`GET /repos/${owner}/${repo}/languages`),
        octokit.request(`GET /repos/${owner}/${repo}/commits`),
        octokit.request(`GET /repos/${owner}/${repo}/contributors`),
        octokit.request(`GET /repos/${owner}/${repo}/contents`)
    ]);
    return{
        name:repoInfo.name,
        description:repoInfo.description,
        language:language,
        commits:commits,
        contributors:contributors,
        content:content
    }
}
getRepoDetail(repoUrl).then((data) => {
    console.log(data);
}).catch((err) => {
    console.log(err);
});
