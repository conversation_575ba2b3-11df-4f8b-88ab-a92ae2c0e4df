{"name": "api", "version": "0.0.0", "private": true, "scripts": {"build": "tsc", "clean": "rm -rf dist", "dev": "nodemon --exec \"node -r esbuild-register ./src/index.ts\" -e .ts", "lint": "tsc --noEmit && eslint \"src/**/*.ts*\" --max-warnings 0", "start": "node -r esbuild-register ./src/index.ts", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "jest": {"preset": "@repo/jest-presets/node"}, "dependencies": {"@repo/logger": "workspace:*", "body-parser": "^1.20.2", "cors": "^2.8.5", "express": "^4.18.3", "morgan": "^1.10.0", "octokit": "^5.0.3"}, "devDependencies": {"@jest/globals": "^29.7.0", "@repo/eslint-config": "workspace:*", "@repo/jest-presets": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/body-parser": "^1.19.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/morgan": "^1.9.9", "@types/node": "^20.11.24", "@types/supertest": "^6.0.2", "esbuild": "^0.20.1", "esbuild-register": "^3.5.0", "eslint": "^8.57.0", "jest": "^29.7.0", "nodemon": "^3.1.0", "supertest": "^6.3.3", "typescript": "5.5.4"}}